import type { FC } from 'react';
import { Outlet } from 'react-router-dom';
import { useResponsive } from '../../../hooks/useResponsive';
import { Header } from '../Header';
import { SidebarNavigation } from '../SidebarNavigation';
import { FooterNavigation } from '../FooterNavigation';

import styles from './index.module.scss';

export const AdminLayout: FC = () => {
  const { isMobile } = useResponsive();

  return (
    <div className={styles.container}>
      <SidebarNavigation />
      <div className={styles.mainWrapper}>
        <Header />
        <div className={styles.contentWrapper}>
          <main className={styles.mainContent}>
            <Outlet />
          </main>
        </div>
        {isMobile && <FooterNavigation />}
      </div>
    </div>
  );
};
