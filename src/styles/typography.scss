// Typography styles based on design system
// Font sizes and line heights from design specifications

// Text styles
.text-tiny {
  font-size: 10px;
  line-height: auto;
  font-weight: var(--font-weight-normal);
}

.text-large {
  font-size: 18px;
  line-height: 28px;
  font-weight: var(--font-weight-normal);
}

.text-lead {
  font-size: 16px;
  line-height: 28px;
  font-weight: var(--font-weight-normal);
}

.text-list {
  font-size: 15px;
  line-height: 24px;
  font-weight: var(--font-weight-normal);
}

// Subtitle styles
.subtitle-regular {
  font-size: 12px;
  line-height: 20px;
  font-weight: var(--font-weight-normal);
}

.subtitle-semibold {
  font-size: 12px;
  line-height: 20px;
  font-weight: var(--font-weight-semibold);
}

// Paragraph styles
.paragraph-regular {
  font-size: 15px;
  line-height: 28px;
  font-weight: var(--font-weight-normal);
}

.paragraph-semibold {
  font-size: 15px;
  line-height: 28px;
  font-weight: var(--font-weight-semibold);
}

// Small text styles
.small-medium {
  font-size: 14px;
  line-height: 20px;
  font-weight: var(--font-weight-medium);
}

.small-regular {
  font-size: 14px;
  line-height: 20px;
  font-weight: var(--font-weight-normal);
}

.small-bold {
  font-size: 14px;
  line-height: 20px;
  font-weight: var(--font-weight-bold);
}

// H5 styles
.h5-base {
  font-size: 18px;
  line-height: 28px;
  font-weight: var(--font-weight-normal);
}

.h5-caps {
  font-size: 18px;
  line-height: 28px;
  font-weight: var(--font-weight-normal);
  text-transform: uppercase;
}

// H4 styles
.h4-base {
  font-size: 20px;
  line-height: 28px;
  font-weight: var(--font-weight-normal);
}

.h4-caps {
  font-size: 20px;
  line-height: 28px;
  font-weight: var(--font-weight-normal);
  text-transform: uppercase;
}

// H3 styles
.h3-base {
  font-size: 24px;
  line-height: 32px;
  font-weight: var(--font-weight-normal);
}

.h3-caps {
  font-size: 24px;
  line-height: 32px;
  font-weight: var(--font-weight-normal);
  text-transform: uppercase;
}

// H2 styles
.h2-base {
  font-size: 32px;
  line-height: 36px;
  font-weight: var(--font-weight-normal);
}

.h2-caps {
  font-size: 32px;
  line-height: 36px;
  font-weight: var(--font-weight-normal);
  text-transform: uppercase;
}

// H1 styles
.h1-base {
  font-size: 40px;
  line-height: 48px;
  font-weight: var(--font-weight-normal);
}

.h1-caps {
  font-size: 40px;
  line-height: 48px;
  font-weight: var(--font-weight-normal);
  text-transform: uppercase;
}

// Semantic HTML elements with default styles
h1 {
  @extend .h1-base;
  margin: 0;
}

h2 {
  @extend .h2-base;
  margin: 0;
}

h3 {
  @extend .h3-base;
  margin: 0;
}

h4 {
  @extend .h4-base;
  margin: 0;
}

h5 {
  @extend .h5-base;
  margin: 0;
}

h6 {
  @extend .small-bold;
  margin: 0;
}

p {
  @extend .paragraph-regular;
  margin: 0;
}

// Utility classes for common text modifications
.text-bold {
  font-weight: var(--font-weight-bold);
}

.text-semibold {
  font-weight: var(--font-weight-semibold);
}

.text-medium {
  font-weight: var(--font-weight-medium);
}

.text-regular {
  font-weight: var(--font-weight-normal);
}

.text-uppercase {
  text-transform: uppercase;
}

.text-lowercase {
  text-transform: lowercase;
}

.text-capitalize {
  text-transform: capitalize;
}
