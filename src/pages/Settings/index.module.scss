@use '../../styles/media' as *;

.settingsPage {
  min-height: 100vh;
  background-color: var(--neutral-dark-bg);
  padding-top: 40px;
  padding-bottom: 40px;

  @include mobile {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;

  @include mobile {
    padding: 0 15px;
  }
}

.header {
  margin-bottom: 32px;

  @include mobile {
    margin-bottom: 24px;
  }
}

.title {
  color: var(--white);
  font-size: var(--font-size-heading);
  font-weight: var(--font-weight-bold);
  margin: 0;

  @include mobile {
    font-size: var(--font-size-xxl);
  }
}

.content {
  background-color: var(--neutral-800);
  border-radius: 12px;
  padding: 0;
  overflow: hidden;
}

.settingsTabs {
  :global(.ant-tabs-nav) {
    background-color: var(--neutral-800);
    margin: 0;
    padding: 32px 40px 0;
    border-bottom: none;

    @include mobile {
      padding: 24px 15px 0;
    }
  }

  :global(.ant-tabs-nav-wrap) {
    background-color: var(--neutral-800);
  }

  :global(.ant-tabs-nav-list) {
    border-bottom: none;
    gap: 8px;
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 0;
  }

  :global(.ant-tabs-tab) {
    background-color: var(--neutral-600);
    color: var(--neutral-300);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-bold);
    padding: 10px 20px;
    margin: 0;
    border-radius: 50px;
    border: none;
    transition: all 0.2s ease;
    height: auto;
    min-height: 37px;
    display: flex;
    align-items: center;
    justify-content: center;

    @include mobile {
      font-size: var(--font-size-xs);
      padding: 8px 12px;
      min-height: 33px;
    }

    &:hover {
      background-color: var(--neutral-500);
      color: var(--white);
    }

    &.ant-tabs-tab-active {
      background-color: var(--primary-500);
      color: var(--neutral-900);
      box-shadow: 0 3px 0 var(--primary-button-shadow);
    }

    :global(.ant-tabs-tab-btn) {
      color: inherit;
      font-weight: inherit;
      font-size: inherit;
    }
  }

  :global(.ant-tabs-content-holder) {
    background-color: var(--neutral-800);
    padding: 40px;

    @include mobile {
      padding: 24px 15px;
    }
  }

  :global(.ant-tabs-tabpane) {
    background-color: var(--neutral-800);
  }

  :global(.ant-tabs-ink-bar) {
    display: none;
  }
}
