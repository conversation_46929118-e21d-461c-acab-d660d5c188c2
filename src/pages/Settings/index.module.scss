@use '../../styles/media' as *;

.settingsPage {
  min-height: 100vh;
  background-color: var(--neutral-dark-bg);
  padding-top: 40px;
  padding-bottom: 40px;

  @include mobile {
    padding-top: 20px;
    padding-bottom: 20px;
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 40px;

  @include mobile {
    padding: 0 15px;
  }
}

.header {
  margin-bottom: 32px;

  @include mobile {
    margin-bottom: 24px;
  }
}

.title {
  color: var(--white);
  font-size: var(--font-size-heading);
  font-weight: var(--font-weight-bold);
  margin: 0;

  @include mobile {
    font-size: var(--font-size-xxl);
  }
}

.content {
  background-color: var(--neutral-800);
  border-radius: 12px;
  padding: 0;
  overflow: hidden;
}

.settingsTabs {
  :global(.ant-tabs-nav) {
    background-color: var(--neutral-700);
    margin: 0;
    padding: 0 40px;

    @include mobile {
      padding: 0 15px;
    }
  }

  :global(.ant-tabs-nav-wrap) {
    background-color: var(--neutral-700);
  }

  :global(.ant-tabs-nav-list) {
    border-bottom: none;
  }

  :global(.ant-tabs-tab) {
    color: var(--neutral-300);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
    padding: 16px 0;
    margin-right: 32px;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;

    @include mobile {
      margin-right: 20px;
      font-size: var(--font-size-xs);
    }

    &:hover {
      color: var(--primary-500);
    }

    &.ant-tabs-tab-active {
      color: var(--primary-500);
      border-bottom-color: var(--primary-500);
    }
  }

  :global(.ant-tabs-content-holder) {
    background-color: var(--neutral-800);
    padding: 40px;

    @include mobile {
      padding: 24px 15px;
    }
  }

  :global(.ant-tabs-tabpane) {
    background-color: var(--neutral-800);
  }

  :global(.ant-tabs-ink-bar) {
    display: none;
  }
}
