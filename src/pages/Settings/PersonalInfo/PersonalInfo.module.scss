@use '../../../styles/media' as *;

.personalInfo {
  .header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;

    @include mobile {
      margin-bottom: 24px;
    }
  }

  .title {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--white);
    margin: 0;

    @include mobile {
      font-size: var(--font-size-lg);
    }
  }

  .saveButton {
    min-width: 80px;
  }

  .form {
    margin-bottom: 40px;

    @include mobile {
      margin-bottom: 32px;
    }
  }

  .formRow {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 24px;

    @include mobile {
      grid-template-columns: 1fr;
      gap: 16px;
      margin-bottom: 16px;
    }
  }

  .formItem {
    margin-bottom: 0;

    :global(.ant-form-item-label) {
      padding-bottom: 8px;

      > label {
        color: var(--neutral-300);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
      }
    }
  }

  .input {
    background-color: var(--neutral-700);
    border: 1px solid var(--neutral-600);
    border-radius: 8px;
    color: var(--white);
    padding: 12px 16px;
    height: 48px;

    &::placeholder {
      color: var(--neutral-400);
    }

    &:hover {
      border-color: var(--neutral-500);
    }

    &:focus {
      border-color: var(--primary-500);
      box-shadow: 0 0 0 2px rgba(255, 235, 59, 0.2);
    }
  }

  .passwordSection {
    border-top: 1px solid var(--neutral-700);
    padding-top: 32px;

    @include mobile {
      padding-top: 24px;
    }
  }

  .passwordHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .passwordTitle {
    font-size: var(--font-size-xl);
    font-weight: var(--font-weight-bold);
    color: var(--white);
    margin: 0;

    @include mobile {
      font-size: var(--font-size-lg);
    }
  }

  .changePasswordButton {
    min-width: 140px;

    @include mobile {
      min-width: 120px;
      font-size: var(--font-size-xs);
    }
  }
}
