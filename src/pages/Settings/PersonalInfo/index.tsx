import { Form, Input, DatePicker } from 'antd';
import { useSelector, useDispatch } from 'react-redux';
import { useState } from 'react';
import { type RootState, type AppDispatch } from '../../../store';
import { useUpdateProfileMutation } from '../../../api/userApi';
import { setUser } from '../../../store/slices/authSlice';
import dayjs from 'dayjs';
import styles from './PersonalInfo.module.scss';
import { toast } from 'sonner';

import { AppButton } from '../../../components/AppButton';

interface PersonalInfoFormData {
  name: string;
  surname: string;
  username: string;
  birthday: dayjs.Dayjs;
}

export const PersonalInfo = () => {
  const [form] = Form.useForm();
  const dispatch = useDispatch<AppDispatch>();
  const { user } = useSelector((state: RootState) => state.auth);
  const [updateProfile, { isLoading: isUpdateProfileLoading }] = useUpdateProfileMutation();
  const [isEditing, setIsEditing] = useState(false);

  const handleEditClick = () => {
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    // Сбрасываем форму к исходным значениям
    form.setFieldsValue({
      name: user?.name || '',
      surname: user?.surname || '',
      username: user?.username || '',
      birthday: user?.birthday ? dayjs(user.birthday) : undefined,
    });
  };

  const handleSubmit = async (values: PersonalInfoFormData) => {
    try {
      const updatedUser = await updateProfile({
        name: values.name,
        surname: values.surname,
        username: values.username,
        birthday: values.birthday.format('YYYY-MM-DD'),
      }).unwrap();

      dispatch(setUser(updatedUser));
      setIsEditing(false);

      toast.success('Profile updated successfully');
    } catch (error: unknown) {
      const errorMessage =
        (error as { data?: { message?: string }; message?: string })?.data?.message ||
        (error as { message?: string })?.message ||
        'Failed to update profile';

      toast.error(errorMessage);
      console.error('Update profile error:', error);
    }
  };

  return (
    <>
      <div className={styles.personalInfo}>
        <div className={styles.header}>
          <p className={styles.title}>Profile info</p>
          <div className={styles.headerButtons}>
            {isEditing ? (
              <>
                <AppButton
                  variant="outlined"
                  className={styles.cancelButton}
                  onClick={handleCancelEdit}
                  disabled={isUpdateProfileLoading}
                >
                  Cancel
                </AppButton>
                <AppButton
                  variant="primary"
                  className={styles.saveButton}
                  htmlType="submit"
                  form="profile-form"
                  loading={isUpdateProfileLoading}
                >
                  {isUpdateProfileLoading ? 'Saving...' : 'Save'}
                </AppButton>
              </>
            ) : (
              <AppButton variant="outlined" className={styles.editButton} onClick={handleEditClick}>
                Edit
              </AppButton>
            )}
          </div>
        </div>

        <Form
          id="profile-form"
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            name: user?.name || '',
            surname: user?.surname || '',
            username: user?.username || '',
            birthday: user?.birthday ? dayjs(user.birthday) : undefined,
          }}
          className={styles.form}
        >
          <div className={styles.formRow}>
            <Form.Item
              label="Name"
              name="name"
              className={styles.formItem}
              rules={[{ required: true, message: 'Please enter your name' }]}
            >
              <Input
                placeholder={user?.name || 'Enter your name'}
                className={styles.input}
                disabled={!isEditing}
              />
            </Form.Item>

            <Form.Item
              label="Surname"
              name="surname"
              className={styles.formItem}
              rules={[{ required: true, message: 'Please enter your surname' }]}
            >
              <Input
                placeholder={user?.surname || 'Enter your surname'}
                className={styles.input}
                disabled={!isEditing}
              />
            </Form.Item>
          </div>

          <div className={styles.formRow}>
            <Form.Item
              label="Username"
              name="username"
              className={styles.formItem}
              rules={[{ required: true, message: 'Please enter your username' }]}
            >
              <Input
                placeholder={user?.username || 'Enter your username'}
                className={styles.input}
                disabled={!isEditing}
              />
            </Form.Item>

            <Form.Item
              label="Birthday"
              name="birthday"
              className={styles.formItem}
              rules={[{ required: true, message: 'Please select your birthday' }]}
            >
              <DatePicker
                placeholder={
                  user?.birthday
                    ? dayjs(user.birthday).format('DD.MM.YYYY')
                    : 'Select your birthday'
                }
                className={styles.input}
                format="DD.MM.YYYY"
                disabled={!isEditing}
              />
            </Form.Item>
          </div>
        </Form>

        <div className={styles.passwordSection}>
          <div className={styles.passwordHeader}>
            <h3 className={styles.passwordTitle}>Password</h3>
            <AppButton variant="outlined" className={styles.changePasswordButton}>
              Change password
            </AppButton>
          </div>
        </div>
      </div>
    </>
  );
};
