@use '../../styles/media' as *;

.breadcrumbsContainer {
  margin-bottom: 24px;
  padding: 0 40px;

  @include mobile {
    padding: 0 15px;
    margin-bottom: 16px;
  }
}

.breadcrumbs {
  :global(.ant-breadcrumb-link) {
    color: var(--neutral-300);
    transition: color 0.2s ease;

    &:hover {
      color: var(--primary-500);
    }
  }

  :global(.ant-breadcrumb-separator) {
    color: var(--neutral-400);
  }
}

.breadcrumbLink {
  color: var(--neutral-300);
  text-decoration: none;
  transition: color 0.2s ease;

  &:hover {
    color: var(--primary-500);
  }
}

.currentPage {
  color: var(--white);
  font-weight: var(--font-weight-medium);
}
